mod constants;
mod utils;

use solana_client::rpc_config::RpcSendTransactionConfig;
use solana_sdk::pubkey::Pubkey;
use solana_sdk::transaction::VersionedTransaction;
use solana_sdk::signer::SignerError;
use solana_sdk::instruction::Instruction;
use solana_sdk::hash::Hash;
use solana_sdk::signature::Keypair;
use solana_sdk::address_lookup_table::AddressLookupTableAccount;
use solana_sdk::system_instruction::transfer;
use solana_sdk::signature::Signer;
use solana_sdk::commitment_config::CommitmentConfig;
use solana_trader_client_rust::provider::http::HTTPClient;
use solana_entry::entry::Entry;
use spl_token::instruction::close_account;
use spl_token::instruction::sync_native;
use std::str::FromStr;
use std::sync::atomic::AtomicPtr;
use std::sync::atomic::Ordering;
use std::sync::atomic::AtomicU64;
use std::sync::atomic::AtomicBool;
use spl_associated_token_account::instruction::create_associated_token_account;
use solana_client::rpc_client::RpcClient;
use std::env;
use std::sync::Arc;
use serde::{Deserialize, Serialize};
use jito_protos::shredstream::{
    shredstream_proxy_client::ShredstreamProxyClient, SubscribeEntriesRequest,
};
use tokio::runtime::Runtime;
use std::time::{SystemTime, UNIX_EPOCH};
use chrono;
use reqwest::Client;
use std::sync::{Mutex, OnceLock};
use std::collections::{HashMap, HashSet};
use log::{info, warn, error, debug};

use reqwest::header::{HeaderMap, HeaderValue, COOKIE, USER_AGENT};
use serde_json::Value;
use std::time::Instant;
use std::time::Duration;
use tokio::time;
use std::io::Write;
use std::net::SocketAddr;

use crate::constants::{
    CREATE_DISCRIMINATOR, MINT_CURVE_INDEX, MINT_TOKEN_INDEX, MINT_USER_INDEX,
    PUMP_BUY_IX_DISCRIMINATOR, PUMP_BUY_SELL_CURVE_INDEX, PUMP_BUY_SELL_MINT_INDEX,
    PUMP_BUY_SELL_USER_INDEX, PUMP_FUN_PROGRAM_ID, PUMP_SELL_IX_DISCRIMINATOR,
    get_shredstream_endpoint, get_token_cleanup_time, get_metrics_window,
    get_min_instruction_count, get_min_account_count, get_metrics_output_interval
};


#[derive(Clone, Hash, Eq, PartialEq)]
pub struct TokenPair {
    pub mint_key: Pubkey,
    pub curve_key: Pubkey,
}

pub static SIGNALED_TOKENS: OnceLock<Mutex<HashSet<TokenPair>>> = OnceLock::new();

#[derive(Clone)]
pub struct MonitoredToken {
    pub mint_key: Pubkey,
    pub curve_key: Pubkey,
    pub detection_time: Instant,
    pub creator_address: Pubkey,
    pub creator_sold_time: Option<Instant>,
    // Precise timing fields for activity measurement
    pub creator_sold_time_system: Option<SystemTime>,
    pub metrics_window_start: Option<Instant>,
    pub recent_transactions: Vec<TransactionRecord>,
}

#[derive(Clone, Debug)]
pub struct TransactionRecord {
    pub user_key: Pubkey,
    pub timestamp: Instant,
    pub amount: u64,
    pub is_buy: bool,
    pub signature: String,
    // Transaction success indicators
    pub has_compute_budget: bool,
    pub instruction_count: usize,
    pub account_count: usize,
}

#[derive(Debug, Clone)]
pub struct TokenMetrics {
    pub mint_key: Pubkey,
    pub curve_key: Pubkey,
    pub creator_address: Pubkey,
    pub creator_sold_time: Instant,
    pub window_duration_secs: u64,
    pub buy_count: u64,
    pub buy_amount_total: u64,
    pub sell_count: u64,
    pub sell_amount_total: u64,
    pub total_transactions: u64,
}

pub struct PumpBot {
    pub rpc_client: RpcClient,
    pub payer: Keypair,
    pub blockhash: AtomicPtr<Hash>,
    pub mints: AtomicPtr<Vec<Pubkey>>,
    pub last_slot: AtomicU64,
    pub enabled: AtomicBool,
    pub buy_sell_mode: AtomicBool,
    pub trade_amount: AtomicU64,
    pub mint_key_ptr: AtomicPtr<Pubkey>,
    pub curve_ptr: AtomicPtr<Pubkey>,
    pub associated_vault_ptr: AtomicPtr<Pubkey>,
    pub monitored_tokens: Mutex<HashMap<Pubkey, MonitoredToken>>,
    pub printed_mints: Mutex<HashSet<Pubkey>>,
}
impl PumpBot {

    /// Calculate the next second boundary from a given SystemTime
    /// If creator sells at 10:01:341, this returns 10:02:000
    pub fn get_next_second_boundary(time: SystemTime) -> SystemTime {
        let duration_since_epoch = time.duration_since(UNIX_EPOCH).unwrap();
        let seconds = duration_since_epoch.as_secs();
        let next_second = seconds + 1;
        UNIX_EPOCH + Duration::from_secs(next_second)
    }

    /// Convert SystemTime to Instant for internal timing calculations
    pub fn system_time_to_instant(system_time: SystemTime) -> Instant {
        let now_system = SystemTime::now();
        let now_instant = Instant::now();

        if system_time <= now_system {
            // Past time - calculate how long ago it was
            let duration_ago = now_system.duration_since(system_time).unwrap_or(Duration::ZERO);
            now_instant.checked_sub(duration_ago).unwrap_or(now_instant)
        } else {
            // Future time - calculate how long until then
            let duration_until = system_time.duration_since(now_system).unwrap_or(Duration::ZERO);
            now_instant + duration_until
        }
    }

    pub fn create() -> Self {
        let key = env::var("PRIVATE_KEY").expect("PRIVATE_KEY is not set");
        let payer = Keypair::from_base58_string(&key);
        // println!("pubkey {}", payer.pubkey().to_string());

        let rpc_client = RpcClient::new_with_commitment(
            // String::from("https://api.mainnet-beta.solana.com"),
            String::from("https://mainnet.helius-rpc.com/?api-key=7bf98cdc-5ab3-470d-8956-b204c0b72e53"),
            CommitmentConfig::confirmed(),
        );
        let blockhash = Hash::default();//rpc_client.get_latest_blockhash().expect("error getting blockhash");
        Self {
            rpc_client,
            payer,
            mints: AtomicPtr::new(Box::into_raw(Box::new(Vec::new()))),
            blockhash: AtomicPtr::new(Box::into_raw(Box::new(blockhash))),
            last_slot: AtomicU64::new(0),
            enabled: AtomicBool::new(true),
            buy_sell_mode: AtomicBool::new(true),
            trade_amount: AtomicU64::new(0u64),
            mint_key_ptr: AtomicPtr::new(Box::into_raw(Box::new(Pubkey::default()))),
            curve_ptr: AtomicPtr::new(Box::into_raw(Box::new(Pubkey::default()))),
            associated_vault_ptr: AtomicPtr::new(Box::into_raw(Box::new(Pubkey::default()))),
            monitored_tokens: Mutex::new(HashMap::new()),
            printed_mints: Mutex::new(HashSet::new()),
        }
    }

    pub fn parse_two_u64_fields(data: &[u8]) -> Option<(u64, u64)> {
        if data.len() >= 24 {
            let amount = u64::from_le_bytes(data[8..16].try_into().ok()?);
            let second = u64::from_le_bytes(data[16..24].try_into().ok()?);
            Some((amount, second))
        } else {
            None
        }
    }

    pub fn add_token_to_monitor(&self, mint_key: Pubkey, curve_key: Pubkey, creator_address: Pubkey) {
        let mut monitored = self.monitored_tokens.lock().unwrap();
        monitored.insert(
            mint_key,
            MonitoredToken {
                mint_key,
                curve_key,
                detection_time: Instant::now(),
                creator_address,
                creator_sold_time: None,
                creator_sold_time_system: None,
                metrics_window_start: None,
                recent_transactions: Vec::new(),
            },
        );
    }

    pub fn remove_token(&self, mint_key: &Pubkey) {
        let mut monitored = self.monitored_tokens.lock().unwrap();
        monitored.remove(mint_key);
    }

    /// Heuristic to determine if a transaction is likely successful based on structure
    /// This works without RPC calls by analyzing transaction metadata
    pub fn is_transaction_likely_successful(
        &self,
        tx: &solana_sdk::transaction::VersionedTransaction,
        instruction_count: usize,
    ) -> bool {
        let static_keys = tx.message.static_account_keys();

        // Check 1: Transaction has reasonable number of accounts (successful txs typically have more accounts)
        if static_keys.len() < get_min_account_count() {
            return false;
        }

        // Check 2: Transaction has multiple instructions (compute budget + main instruction)
        if instruction_count < get_min_instruction_count() {
            return false;
        }

        // Check 3: Transaction has signatures (basic validity)
        if tx.signatures.is_empty() {
            return false;
        }

        // Check 4: Look for compute budget instruction (successful txs often include this)
        let compute_budget_program = Pubkey::from_str("ComputeBudget111111111111111111111111111111").unwrap();
        let has_compute_budget = static_keys.contains(&compute_budget_program);

        // Successful transactions typically have compute budget instructions
        // This is a strong indicator on Solana
        has_compute_budget
    }

    /// Verify activity calculation accuracy by checking transaction timestamps
    pub fn verify_activity_calculation(&self, token: &MonitoredToken) -> bool {
        if let Some(metrics_window_start) = token.metrics_window_start {
            let window_duration = Duration::from_secs(get_metrics_window());
            let window_end = metrics_window_start + window_duration;

            // Verify all transactions are within the correct time window
            for tx in &token.recent_transactions {
                if tx.timestamp < metrics_window_start || tx.timestamp > window_end {
                    warn!("⚠️ Transaction outside metrics window detected: mint={} tx_time={:?} window_start={:?}",
                        token.mint_key, tx.timestamp, metrics_window_start);
                    return false;
                }
            }

            debug!("✅ Activity calculation verified for token {}: {} transactions in window",
                token.mint_key, token.recent_transactions.len());
            return true;
        }
        false
    }

    /// Calculate metrics for a token within the configured time window
    /// Uses precise timing: measures activity from next second boundary after creator sells
    pub fn calculate_token_metrics(&self, token: &MonitoredToken) -> Option<TokenMetrics> {
        let metrics_window_start = token.metrics_window_start?;
        let creator_sold_time = token.creator_sold_time?;
        let now = Instant::now();
        let window_duration = Duration::from_secs(get_metrics_window());

        // Only calculate if we're within the window after metrics window started
        if now.duration_since(metrics_window_start) > window_duration {
            // Log when metrics window expires for verification
            debug!("⏰ Metrics window expired for token {}, duration: {}s",
                token.mint_key,
                now.duration_since(metrics_window_start).as_secs()
            );
            return None;
        }

        // Filter transactions within the precise time window
        // Only count transactions from the metrics window start (next second boundary)
        let window_start = metrics_window_start;
        let window_end = metrics_window_start + window_duration;

        let window_transactions: Vec<&TransactionRecord> = token.recent_transactions
            .iter()
            .filter(|tx| tx.timestamp >= window_start && tx.timestamp <= window_end)
            .collect();

        let buy_transactions: Vec<&TransactionRecord> = window_transactions
            .iter()
            .filter(|tx| tx.is_buy)
            .cloned()
            .collect();

        let sell_transactions: Vec<&TransactionRecord> = window_transactions
            .iter()
            .filter(|tx| !tx.is_buy)
            .cloned()
            .collect();

        let metrics = TokenMetrics {
            mint_key: token.mint_key,
            curve_key: token.curve_key,
            creator_address: token.creator_address,
            creator_sold_time,
            window_duration_secs: get_metrics_window(),
            buy_count: buy_transactions.len() as u64,
            buy_amount_total: buy_transactions.iter().map(|tx| tx.amount).sum(),
            sell_count: sell_transactions.len() as u64,
            sell_amount_total: sell_transactions.iter().map(|tx| tx.amount).sum(),
            total_transactions: window_transactions.len() as u64,
        };

        // Final validation: ensure window duration matches configuration
        if metrics.window_duration_secs != get_metrics_window() {
            error!("❌ Metrics window duration mismatch: expected {}, got {}",
                get_metrics_window(), metrics.window_duration_secs);
        }

        Some(metrics)
    }

    pub async fn monitor_tokens(bot: Arc<Self>) {
        let mut last_cleanup_time = Instant::now();
        let mut last_metrics_time = Instant::now();

        loop {
            time::sleep(Duration::from_millis(300)).await;

            let now = Instant::now();

            // Output metrics at configurable interval
            if now.duration_since(last_metrics_time) >= Duration::from_secs(get_metrics_output_interval()) {
                bot.output_token_metrics().await;
                last_metrics_time = now;
            }

            // Cleanup old tokens every 30 minutes (1800 seconds)
            if now.duration_since(last_cleanup_time) >= Duration::from_secs(1800) {
                let mut to_remove = Vec::new();
                {
                    let monitored = bot.monitored_tokens.lock().unwrap();
                    for (mint_key, token) in monitored.iter() {
                        if now.duration_since(token.detection_time) >= Duration::from_secs(get_token_cleanup_time()) {
                            to_remove.push(*mint_key);
                        }
                    }
                }

                if !to_remove.is_empty() {
                    for mint_key in to_remove {
                        bot.remove_token(&mint_key);
                    }
                }
                last_cleanup_time = now;
            }
        }
    }

    /// Output metrics for all monitored tokens that have creator sold events
    pub async fn output_token_metrics(&self) {
        let monitored_snapshot = {
            let monitored = self.monitored_tokens.lock().unwrap();
            monitored.clone()
        };

        for (mint_key, token) in monitored_snapshot {
            // Only process tokens where creator has sold
            if token.creator_sold_time.is_none() {
                continue;
            }

            if let Some(metrics) = self.calculate_token_metrics(&token) {
                // Verify activity calculation accuracy
                self.verify_activity_calculation(&token);

                // Clean up old transactions outside the window
                self.cleanup_old_transactions(&mint_key).await;

                // Debug logging for activity calculation verification
                debug!("Token {} metrics: Buy: {} (total: {}), Sell: {} (total: {}), Window: {}s",
                    metrics.mint_key,
                    metrics.buy_count,
                    metrics.buy_amount_total,
                    metrics.sell_count,
                    metrics.sell_amount_total,
                    metrics.window_duration_secs
                );

                if metrics.buy_count > 3 && metrics.buy_count - 1 > metrics.sell_count && metrics.buy_amount_total > metrics.sell_amount_total {
                    // Check if we've already printed this mint
                    let mut printed_mints = self.printed_mints.lock().unwrap();
                    if !printed_mints.contains(&mint_key) {
                        info!("🎯 SIGNAL: Mint: {} Creator: {} | Buy: {} | Sell: {} | Window: {}s",
                            metrics.mint_key,
                            metrics.creator_address,
                            metrics.buy_count,
                            metrics.sell_count,
                            metrics.window_duration_secs
                        );
                        printed_mints.insert(mint_key);
                    }
                }
            }
        }
    }

    /// Clean up old transaction records outside the monitoring window
    async fn cleanup_old_transactions(&self, mint_key: &Pubkey) {
        let mut monitored = self.monitored_tokens.lock().unwrap();
        if let Some(token) = monitored.get_mut(mint_key) {
            if let Some(metrics_window_start) = token.metrics_window_start {
                let window_duration = Duration::from_secs(get_metrics_window());
                let cutoff_time = metrics_window_start + window_duration;
                let now = Instant::now();

                // Remove transactions outside the precise metrics window
                token.recent_transactions.retain(|tx| {
                    tx.timestamp >= metrics_window_start && tx.timestamp <= cutoff_time && now <= cutoff_time
                });
            }
        }
    }

    /*pub async fn print_monitored_tokens(bot: Arc<Self>) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        dotenv::dotenv().ok();

        loop {
            time::sleep(Duration::from_millis(300)).await;

            let monitored_snapshot = {
                let monitored = bot.monitored_tokens.lock().unwrap();
                monitored.clone()
            };

            for (mint_key, token) in monitored_snapshot {
                if token.total_sellers > 0 || token.total_buyers <= 4 {
                    continue;
                }

                let should_process = {
                    // Removed BOUGHT_TOKENS logic related to printing
                    true
                };

                if !should_process {
                    continue;
                }

                // Rest of the print_monitored_tokens function...
                // ... existing code ...
            }
        }
    }*/


    pub async fn process_entries(&self, entries: Vec<Entry>, _slot: u64, _start: u32, _end: u32) -> bool {
        // println!("slot {}, start: {}, end: {}", slot, start, end);
        if self.enabled.load(Ordering::SeqCst) {

            let pump_pid = Pubkey::from_str(constants::PUMP_FUN_PROGRAM_ID).unwrap();
            let mut _processed_count = 0;

            for entry in entries.iter() {
                for tx in entry.transactions.iter() {
                    let static_keys = tx.message.static_account_keys();

                    if static_keys.contains(&pump_pid){
                        let signature = tx.signatures.get(0).cloned().unwrap_or_default();
                        let signature_str = signature.to_string();

                        // Count total instructions for success heuristic
                        let total_instruction_count = tx.message.instructions().len();

                        // Check if transaction is likely successful using our heuristic
                        let is_likely_successful = self.is_transaction_likely_successful(tx, total_instruction_count);

                        // Skip failed transactions early
                        if !is_likely_successful {
                            continue;
                        }

                        for ix in tx.message.instructions() {
                            let ix_pid: &Pubkey = &static_keys[ix.program_id_index as usize];

                            if ix_pid.eq(&pump_pid) {
                                _processed_count += 1;
                                let is_new_token = ix.data.starts_with(&constants::CREATE_DISCRIMINATOR);
                                let is_buy = ix.data.starts_with(&constants::PUMP_BUY_IX_DISCRIMINATOR);
                                let is_sell = ix.data.starts_with(&constants::PUMP_SELL_IX_DISCRIMINATOR);

                                // Skip if instruction doesn't have enough accounts
                                if ix.accounts.len() <= constants::PUMP_BUY_SELL_USER_INDEX {
                                    continue;
                                }

                                // Check each account index individually
                                let mint_idx = ix.accounts[constants::PUMP_BUY_SELL_MINT_INDEX] as usize;
                                let curve_idx = ix.accounts[constants::PUMP_BUY_SELL_CURVE_INDEX] as usize;
                                let user_idx = ix.accounts[constants::PUMP_BUY_SELL_USER_INDEX] as usize;

                                // Skip if any index is out of bounds
                                if mint_idx >= static_keys.len() ||
                                   curve_idx >= static_keys.len() ||
                                   user_idx >= static_keys.len() {
                                    continue;
                                }

                                if is_sell {
                                    let mint_key = &static_keys[mint_idx];
                                    let curve_key = &static_keys[curve_idx];
                                    let user_key = &static_keys[user_idx];
                                    if let Some((amount, _min_sol_output)) = Self::parse_two_u64_fields(&ix.data) {
                                        let mut monitored = self.monitored_tokens.lock().unwrap();
                                        if let Some(token) = monitored.get_mut(mint_key) {

                                            // Check if creator sold
                                            if user_key.eq(&token.creator_address) {
                                                let creator_sell_time_system = SystemTime::now();
                                                let next_second_boundary = Self::get_next_second_boundary(creator_sell_time_system);
                                                let metrics_window_start_instant = Self::system_time_to_instant(next_second_boundary);

                                                token.creator_sold_time = Some(Instant::now());
                                                token.creator_sold_time_system = Some(creator_sell_time_system);
                                                token.metrics_window_start = Some(metrics_window_start_instant);

                                                info!("🔴 Creator Sold: mint={} creator={} | Metrics window starts at next second boundary",
                                                    mint_key, token.creator_address);
                                            }

                                            // Record the sell transaction if we're monitoring this token
                                            // Only record transactions that occur after the metrics window starts
                                            if let Some(metrics_window_start) = token.metrics_window_start {
                                                let tx_timestamp = Instant::now();

                                                // Only record if transaction occurs after metrics window start
                                                if tx_timestamp >= metrics_window_start {
                                                    let compute_budget_program = Pubkey::from_str("ComputeBudget111111111111111111111111111111").unwrap();
                                                    let has_compute_budget = static_keys.contains(&compute_budget_program);

                                                    let tx_record = TransactionRecord {
                                                        user_key: *user_key,
                                                        timestamp: tx_timestamp,
                                                        amount,
                                                        is_buy: false,
                                                        signature: signature_str.clone(),
                                                        has_compute_budget,
                                                        instruction_count: total_instruction_count,
                                                        account_count: static_keys.len(),
                                                    };

                                                    token.recent_transactions.push(tx_record);
                                                    debug!("📊 Recorded SELL: mint={} user={} amount={}", mint_key, user_key, amount);
                                                } else {
                                                    debug!("⏰ Excluded SELL (before window): mint={} user={}", mint_key, user_key);
                                                }
                                            }
                                        }
                                    }
                                }
                                if is_buy {
                                    let mint_key = &static_keys[mint_idx];
                                    let curve_key = &static_keys[curve_idx];
                                    let user_key = &static_keys[user_idx];
                                    if let Some((amount, _max_sol_cost)) = Self::parse_two_u64_fields(&ix.data) {
                                        let mut monitored = self.monitored_tokens.lock().unwrap();
                                        if let Some(token) = monitored.get_mut(mint_key) {
                                            // Only record buy transactions after metrics window starts
                                            if let Some(metrics_window_start) = token.metrics_window_start {
                                                let tx_timestamp = Instant::now();

                                                // Only record if transaction occurs after metrics window start
                                                if tx_timestamp >= metrics_window_start {
                                                    let compute_budget_program = Pubkey::from_str("ComputeBudget111111111111111111111111111111").unwrap();
                                                    let has_compute_budget = static_keys.contains(&compute_budget_program);

                                                    let tx_record = TransactionRecord {
                                                        user_key: *user_key,
                                                        timestamp: tx_timestamp,
                                                        amount,
                                                        is_buy: true,
                                                        signature: signature_str.clone(),
                                                        has_compute_budget,
                                                        instruction_count: total_instruction_count,
                                                        account_count: static_keys.len(),
                                                    };

                                                    token.recent_transactions.push(tx_record);
                                                    debug!("� Recorded BUY: mint={} user={} amount={}", mint_key, user_key, amount);
                                                } else {
                                                    debug!("⏰ Excluded BUY (before window): mint={} user={}", mint_key, user_key);
                                                }
                                            }
                                        }
                                    }
                                }
                                if is_new_token {
                                    // Skip if instruction doesn't have enough accounts for new token
                                    if ix.accounts.len() <= constants::MINT_USER_INDEX {
                                        continue;
                                    }

                                    // Check each account index individually for new token
                                    let mint_idx = ix.accounts[constants::MINT_TOKEN_INDEX] as usize;
                                    let curve_idx = ix.accounts[constants::MINT_CURVE_INDEX] as usize;
                                    let user_idx = ix.accounts[constants::MINT_USER_INDEX] as usize;

                                    // Skip if any index is out of bounds
                                    if mint_idx >= static_keys.len() ||
                                       curve_idx >= static_keys.len() ||
                                       user_idx >= static_keys.len() {
                                        continue;
                                    }

                                    let mint_key = &static_keys[mint_idx];
                                    let curve_key = &static_keys[curve_idx];
                                    let user_key = &static_keys[user_idx];

                                    self.add_token_to_monitor(*mint_key, *curve_key, *user_key);
                                    return true;
                                }
                            }
                        }
                    }

                }
            }

        }
        return false;
    }
  
 
    pub fn get_cur_time_ms() -> String {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .expect("Time went backwards");

        let secs = now.as_secs();
        let millis = now.subsec_millis();
        let datetime = chrono::DateTime::from_timestamp(secs as i64, 0).unwrap();
        format!("{}:{:03}", datetime.format("%Y-%m-%d %H:%M:%S"), millis)
    }
    pub fn receive_entry_update(bot_param: Arc<PumpBot>) {
        let bot = Arc::clone(&bot_param);
        std::thread::spawn(move || {
            let rt = Runtime::new().unwrap();
            
            let _result = rt.block_on(
                PumpBot::run_entry_update(bot)
            );
        });
        
    }
    pub async fn run_entry_update(bot: Arc<PumpBot>) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        info!("Program Started: {}", Self::get_cur_time_ms());
        let shredstream_endpoint = get_shredstream_endpoint();
        info!("Connecting to Jito Shredstream at: {}", shredstream_endpoint);
        SIGNALED_TOKENS.get_or_init(|| Mutex::new(HashSet::new()));

        let mut client = match ShredstreamProxyClient::connect(shredstream_endpoint.clone()).await {
            Ok(client) => {
                info!("✅ Successfully connected to Jito Shredstream!");
                client
            },
            Err(e) => {
                error!("❌ Failed to connect to Jito Shredstream: {}", e);
                error!("Please ensure the Jito Shredstream service is running at {}", shredstream_endpoint);
                return Err(Box::new(e));
            }
        };
        let mut stream = match client.subscribe_entries(SubscribeEntriesRequest {}).await {
            Ok(response) => {
                info!("✅ Successfully subscribed to entry stream!");
                response.into_inner()
            },
            Err(e) => {
                error!("❌ Failed to subscribe to entries: {}", e);
                return Err(Box::new(e));
            }
        };

        // Start the monitoring task once - this prevents resource exhaustion
        let monitor_bot = Arc::clone(&bot);
        tokio::spawn(async move {
            PumpBot::monitor_tokens(monitor_bot).await;
        });

        info!("🔍 Token monitoring task started");

        loop {
            match stream.message().await {
                Ok(Some(slot_entry)) => {
                    let entries = match bincode::deserialize::<Vec<solana_entry::entry::Entry>>(&slot_entry.entries) {
                        Ok(e) => e,
                        Err(e) => {
                            error!("Deserialization failed with err: {}", e);
                            continue;
                        }
                    };

                    let bot_clone = Arc::clone(&bot);
                    let slot = slot_entry.slot as u64;

                    // Process entries without spawning excessive tasks
                    tokio::spawn(async move {
                        bot_clone.process_entries(entries, slot, 0, 0).await;
                    });
                }
                Ok(None) => {
                    warn!("🛑 Stream ended gracefully");
                    break;
                }
                Err(e) => {
                    error!("❌ Stream error: {}", e);
                    return Err(Box::new(e));
                }
            }
        }

        warn!("🛑 Entry stream ended - this should not happen during normal operation");
        Ok(())
    }
}


pub fn get_ata_ix(mint_key: &Pubkey, owner: &Keypair) -> Instruction {
    create_associated_token_account(&owner.pubkey(), &owner.pubkey(), mint_key, &spl_token::ID)
}
pub fn get_sync_ix(account_pubkey: &Pubkey) -> Instruction {
    sync_native(&spl_token::id(), account_pubkey).expect("getting sync ix error")
}
pub fn get_transfer_ix(from_pubkey: &Pubkey, to_pubkey: &Pubkey, lamports: u64) -> Instruction {
    transfer(from_pubkey, to_pubkey, lamports)
}
pub fn get_close_ix(close_address: &Pubkey, recipient_address: &Pubkey, authority_address: &Pubkey) -> Instruction {
    close_account(&spl_token::id(), close_address, recipient_address, authority_address, &[authority_address]).expect("close ix error")
}
pub fn get_jup_pda_token_account(user: &Pubkey, mint: &Pubkey) -> (Pubkey, u8) {
    let program_id = Pubkey::from_str("JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4").unwrap();
    let seeds = &[
        b"token_account",
        user.as_ref(),
        mint.as_ref(),
    ];
    Pubkey::find_program_address(seeds, &program_id)
}



pub fn get_versioned_tx(
    payer: &Keypair,
    tx_instructions: &[Instruction],
    lookuptables: &[AddressLookupTableAccount],
    blockhash: Hash,
    signers: &Vec<&Keypair>
) -> std::result::Result<VersionedTransaction, SignerError> {
    // let signers = vec![&payer];

    let versioned_message = solana_sdk::message::VersionedMessage::V0(
        solana_sdk::message::v0::Message::try_compile(
            &payer.pubkey(),
            tx_instructions,
            lookuptables,
            blockhash,
        )
        .unwrap(),
    );
    VersionedTransaction::try_new(versioned_message, signers)
}
